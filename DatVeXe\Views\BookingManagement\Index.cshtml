@model IEnumerable<DatVeXe.Models.ChuyenXe>
@{
    ViewData["Title"] = "Quản lý đặt vé";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary fw-bold">
                    <i class="bi bi-clipboard-check me-2"></i>@ViewData["Title"]
                </h2>
                <div class="d-flex gap-2">
                    <a asp-controller="ChuyenXe" asp-action="Create" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Thêm chuyến xe
                    </a>
                </div>
            </div>

            <!-- Bộ lọc -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Ngày khởi hành</label>
                            <input type="date" name="ngayKhoiHanh" value="@ViewBag.NgayKhoiHanh?.ToString("yyyy-MM-dd")" class="form-control">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Trạng thái</label>
                            <select name="trangThai" class="form-select">
                                <option value="">-- Tất cả --</option>
                                <option value="chua_khoi_hanh" selected="@(ViewBag.TrangThai == "chua_khoi_hanh")">Chưa khởi hành</option>
                                <option value="da_khoi_hanh" selected="@(ViewBag.TrangThai == "da_khoi_hanh")">Đã khởi hành</option>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="bi bi-search me-1"></i>Lọc
                            </button>
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise me-1"></i>Đặt lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Danh sách chuyến xe -->
            @if (Model.Any())
            {
                <div class="row">
                    @foreach (var chuyenXe in Model)
                    {
                        var soVeDaDat = chuyenXe.Ves?.Count ?? 0;
                        var soGheTrong = (chuyenXe.Xe?.SoGhe ?? 0) - soVeDaDat;
                        var daDi = chuyenXe.NgayKhoiHanh <= DateTime.Now;
                        var tileGheDat = chuyenXe.Xe?.SoGhe > 0 ? (double)soVeDaDat / chuyenXe.Xe.SoGhe * 100 : 0;

                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100 shadow-sm">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0 fw-bold">
                                        @chuyenXe.DiemDiDisplay → @chuyenXe.DiemDenDisplay
                                    </h6>
                                    <span class="badge @(daDi ? "bg-secondary" : "bg-success")">
                                        @(daDi ? "Đã đi" : "Chưa đi")
                                    </span>
                                </div>
                                <div class="card-body">
                                    <div class="row g-2 mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">Ngày khởi hành</small>
                                            <div class="fw-semibold">@chuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy")</div>
                                            <div class="text-primary">@chuyenXe.NgayKhoiHanh.ToString("HH:mm")</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Xe</small>
                                            <div class="fw-semibold">@chuyenXe.Xe?.BienSo</div>
                                            <div class="text-muted small">@chuyenXe.Xe?.LoaiXe</div>
                                        </div>
                                    </div>

                                    @if (chuyenXe.TaiXe != null)
                                    {
                                        <div class="mb-3">
                                            <small class="text-muted">Tài xế</small>
                                            <div class="fw-semibold">@chuyenXe.TaiXe.HoTen</div>
                                            <div class="text-muted small">@chuyenXe.TaiXe.SoDienThoai</div>
                                        </div>
                                    }

                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <small class="text-muted">Tình trạng ghế</small>
                                            <small class="fw-semibold">@soVeDaDat/@(chuyenXe.Xe?.SoGhe ?? 0)</small>
                                        </div>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar @(tileGheDat > 80 ? "bg-danger" : tileGheDat > 50 ? "bg-warning" : "bg-success")" 
                                                 style="width: @(tileGheDat)%"></div>
                                        </div>
                                        <small class="text-muted">@(tileGheDat.ToString("F1"))% đã đặt</small>
                                    </div>

                                    @if (soVeDaDat > 0)
                                    {
                                        <div class="mb-3">
                                            <small class="text-muted">Thống kê vé</small>
                                            <div class="row g-1 mt-1">
                                                @{
                                                    var veDaDat = chuyenXe.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaDat || v.TrangThai == TrangThaiVe.DaThanhToan) ?? 0;
                                                    var veDaSuDung = chuyenXe.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaSuDung) ?? 0;
                                                    var veDaHuy = chuyenXe.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaHuy) ?? 0;
                                                }
                                                <div class="col-4 text-center">
                                                    <div class="badge bg-primary w-100">@veDaDat</div>
                                                    <small class="text-muted d-block">Đã đặt</small>
                                                </div>
                                                <div class="col-4 text-center">
                                                    <div class="badge bg-success w-100">@veDaSuDung</div>
                                                    <small class="text-muted d-block">Đã đón</small>
                                                </div>
                                                <div class="col-4 text-center">
                                                    <div class="badge bg-danger w-100">@veDaHuy</div>
                                                    <small class="text-muted d-block">Đã hủy</small>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="d-flex gap-2">
                                        <a asp-action="Details" asp-route-id="@chuyenXe.ChuyenXeId" 
                                           class="btn btn-primary btn-sm flex-fill">
                                            <i class="bi bi-eye me-1"></i>Chi tiết (@soVeDaDat)
                                        </a>
                                        @if (soVeDaDat > 0)
                                        {
                                            <a asp-action="ExportPassengerList" asp-route-id="@chuyenXe.ChuyenXeId" 
                                               class="btn btn-outline-success btn-sm" title="Xuất danh sách">
                                                <i class="bi bi-download"></i>
                                            </a>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="bi bi-calendar-x display-1 text-muted"></i>
                    </div>
                    <h4 class="text-muted">Không có chuyến xe nào</h4>
                    <p class="text-muted">Không tìm thấy chuyến xe nào phù hợp với bộ lọc.</p>
                    <a asp-controller="ChuyenXe" asp-action="Create" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Thêm chuyến xe mới
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Styles {
    <style>
        .progress {
            border-radius: 10px;
        }
        .card {
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .badge {
            font-size: 0.75rem;
        }
    </style>
}
