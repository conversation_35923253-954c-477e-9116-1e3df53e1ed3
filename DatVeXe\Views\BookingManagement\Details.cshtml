@model DatVeXe.Models.ChuyenXe
@{
    ViewData["Title"] = "Chi tiết đặt vé - " + Model.DiemDiDisplay + " → " + Model.DiemDenDisplay;
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary fw-bold">
                    <i class="bi bi-clipboard-check me-2"></i>Chi tiết đặt vé
                </h2>
                <div class="d-flex gap-2">
                    <a asp-action="ExportPassengerList" asp-route-id="@Model.ChuyenXeId" 
                       class="btn btn-success">
                        <i class="bi bi-download me-2"></i>Xuất danh sách
                    </a>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Quay lại
                    </a>
                </div>
            </div>

            <!-- Thông tin chuyến xe -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-bus-front me-2"></i>Thông tin chuyến xe
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-semibold">Tuyến đường:</td>
                                    <td>@Model.DiemDiDisplay → @Model.DiemDenDisplay</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Ngày khởi hành:</td>
                                    <td>@Model.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Xe:</td>
                                    <td>@Model.Xe?.BienSo (@Model.Xe?.LoaiXe)</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-semibold">Tài xế:</td>
                                    <td>@(Model.TaiXe?.HoTen ?? "Chưa gán") @(Model.TaiXe?.SoDienThoai != null ? $"({Model.TaiXe.SoDienThoai})" : "")</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Số ghế:</td>
                                    <td>@Model.Xe?.SoGhe ghế</td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Đã đặt:</td>
                                    <td>@(Model.Ves?.Count ?? 0) vé</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            @if (Model.Ves?.Any() == true)
            {
                <!-- Thống kê nhanh -->
                <div class="row mb-4">
                    @{
                        var veDaDat = Model.Ves.Count(v => v.TrangThai == TrangThaiVe.DaDat || v.TrangThai == TrangThaiVe.DaThanhToan);
                        var veDaSuDung = Model.Ves.Count(v => v.TrangThai == TrangThaiVe.DaSuDung);
                        var veDaHoanThanh = Model.Ves.Count(v => v.TrangThai == TrangThaiVe.DaHoanThanh);
                        var veDaHuy = Model.Ves.Count(v => v.TrangThai == TrangThaiVe.DaHuy);
                    }
                    <div class="col-md-3">
                        <div class="card text-center bg-primary text-white">
                            <div class="card-body">
                                <h3 class="mb-0">@veDaDat</h3>
                                <small>Đã đặt / Đã thanh toán</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-success text-white">
                            <div class="card-body">
                                <h3 class="mb-0">@veDaSuDung</h3>
                                <small>Đã đón khách</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-info text-white">
                            <div class="card-body">
                                <h3 class="mb-0">@veDaHoanThanh</h3>
                                <small>Đã hoàn thành</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-danger text-white">
                            <div class="card-body">
                                <h3 class="mb-0">@veDaHuy</h3>
                                <small>Đã hủy</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Danh sách vé -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-ticket-perforated me-2"></i>Danh sách vé (@Model.Ves.Count)
                        </h5>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                                <i class="bi bi-check-all me-1"></i>Chọn tất cả
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-gear me-1"></i>Thao tác hàng loạt
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="updateMultipleStatus('DaSuDung')">
                                        <i class="bi bi-check-circle text-success me-2"></i>Đánh dấu đã đón
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="updateMultipleStatus('DaHoanThanh')">
                                        <i class="bi bi-check-circle-fill text-info me-2"></i>Đánh dấu hoàn thành
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="updateMultipleStatus('DaHuy')">
                                        <i class="bi bi-x-circle text-danger me-2"></i>Hủy vé
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                        </th>
                                        <th>Khách hàng</th>
                                        <th>Liên hệ</th>
                                        <th>Ghế</th>
                                        <th>Trạng thái</th>
                                        <th>Ngày đặt</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var ve in Model.Ves.OrderBy(v => v.ChoNgoi?.SoGhe))
                                    {
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="ticket-checkbox" value="@ve.VeId">
                                            </td>
                                            <td>
                                                <div class="fw-semibold">@ve.TenKhach</div>
                                                @if (!string.IsNullOrEmpty(ve.GhiChu))
                                                {
                                                    <small class="text-muted">@ve.GhiChu</small>
                                                }
                                            </td>
                                            <td>
                                                <div>@ve.SoDienThoai</div>
                                                @if (!string.IsNullOrEmpty(ve.Email))
                                                {
                                                    <small class="text-muted">@ve.Email</small>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">@ve.ChoNgoi?.SoGhe</span>
                                            </td>
                                            <td>
                                                @{
                                                    var statusClass = ve.TrangThai switch
                                                    {
                                                        TrangThaiVe.DaDat => "bg-primary",
                                                        TrangThaiVe.DaThanhToan => "bg-primary",
                                                        TrangThaiVe.DaSuDung => "bg-success",
                                                        TrangThaiVe.DaHoanThanh => "bg-info",
                                                        TrangThaiVe.DaHuy => "bg-danger",
                                                        _ => "bg-secondary"
                                                    };
                                                    var statusText = ve.TrangThai switch
                                                    {
                                                        TrangThaiVe.DaDat => "Đã đặt",
                                                        TrangThaiVe.DaThanhToan => "Đã thanh toán",
                                                        TrangThaiVe.DaSuDung => "Đã đón",
                                                        TrangThaiVe.DaHoanThanh => "Hoàn thành",
                                                        TrangThaiVe.DaHuy => "Đã hủy",
                                                        _ => "Không xác định"
                                                    };
                                                }
                                                <span class="badge @statusClass">@statusText</span>
                                            </td>
                                            <td>
                                                <div>@ve.NgayDat.ToString("dd/MM/yyyy")</div>
                                                <small class="text-muted">@ve.NgayDat.ToString("HH:mm")</small>
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        Cập nhật
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        @if (ve.TrangThai != TrangThaiVe.DaSuDung)
                                                        {
                                                            <li><a class="dropdown-item" href="#" onclick="updateTicketStatus(@ve.VeId, 'DaSuDung')">
                                                                <i class="bi bi-check-circle text-success me-2"></i>Đã đón khách
                                                            </a></li>
                                                        }
                                                        @if (ve.TrangThai != TrangThaiVe.DaHoanThanh)
                                                        {
                                                            <li><a class="dropdown-item" href="#" onclick="updateTicketStatus(@ve.VeId, 'DaHoanThanh')">
                                                                <i class="bi bi-check-circle-fill text-info me-2"></i>Hoàn thành
                                                            </a></li>
                                                        }
                                                        @if (ve.TrangThai != TrangThaiVe.DaHuy)
                                                        {
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><a class="dropdown-item" href="#" onclick="updateTicketStatus(@ve.VeId, 'DaHuy')">
                                                                <i class="bi bi-x-circle text-danger me-2"></i>Hủy vé
                                                            </a></li>
                                                        }
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="bi bi-ticket-perforated display-1 text-muted"></i>
                    </div>
                    <h4 class="text-muted">Chưa có vé nào được đặt</h4>
                    <p class="text-muted">Chuyến xe này chưa có khách hàng nào đặt vé.</p>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function updateTicketStatus(veId, trangThai) {
            let ghiChu = '';
            if (trangThai === 'DaHuy') {
                ghiChu = prompt('Nhập lý do hủy vé:');
                if (ghiChu === null) return; // User cancelled
            }

            $.ajax({
                url: '@Url.Action("UpdateTicketStatus")',
                type: 'POST',
                data: {
                    veId: veId,
                    trangThai: trangThai,
                    ghiChu: ghiChu
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        location.reload();
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('Có lỗi xảy ra khi cập nhật trạng thái vé');
                }
            });
        }

        function updateMultipleStatus(trangThai) {
            const selectedIds = $('.ticket-checkbox:checked').map(function() {
                return $(this).val();
            }).get();

            if (selectedIds.length === 0) {
                toastr.warning('Vui lòng chọn ít nhất một vé');
                return;
            }

            let ghiChu = '';
            if (trangThai === 'DaHuy') {
                ghiChu = prompt('Nhập lý do hủy vé:');
                if (ghiChu === null) return; // User cancelled
            }

            $.ajax({
                url: '@Url.Action("UpdateMultipleTickets")',
                type: 'POST',
                data: {
                    veIds: selectedIds,
                    trangThai: trangThai,
                    ghiChu: ghiChu
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        location.reload();
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('Có lỗi xảy ra khi cập nhật trạng thái vé');
                }
            });
        }

        function selectAll() {
            $('.ticket-checkbox').prop('checked', true);
            $('#selectAllCheckbox').prop('checked', true);
        }

        function toggleSelectAll() {
            const isChecked = $('#selectAllCheckbox').is(':checked');
            $('.ticket-checkbox').prop('checked', isChecked);
        }

        $(document).ready(function() {
            // Update select all checkbox when individual checkboxes change
            $('.ticket-checkbox').change(function() {
                const totalCheckboxes = $('.ticket-checkbox').length;
                const checkedCheckboxes = $('.ticket-checkbox:checked').length;
                $('#selectAllCheckbox').prop('checked', totalCheckboxes === checkedCheckboxes);
            });
        });
    </script>
}
